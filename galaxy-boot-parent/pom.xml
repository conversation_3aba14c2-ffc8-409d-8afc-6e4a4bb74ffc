<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.7</version>
        <relativePath/>
    </parent>

    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>Galaxy Spring Parent</name>
    <description>Galaxy Boot Parent</description>
    <url>https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot</url>

    <scm>
        <url>https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot</url>
        <connection>
            scm:git:git://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot.git
        </connection>
        <developerConnection>
            scm:git:ssh://****************************/it-infrastructure/galaxy-boot.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <revision>0.5.2</revision>

        <java.version>21</java.version>

        <!-- Spring Boot -->
        <spring-boot.version>3.3.7</spring-boot.version>
        <spring-cloud.version>2023.0.4</spring-cloud.version>
        <jakarta.servlet.version>6.1.0</jakarta.servlet.version>
        <spring-ai.version>1.0.0-RC1</spring-ai.version>

        <!-- SLF4J -->
        <slf4j-bom.version>2.0.16</slf4j-bom.version>
        <log4j-slf4j-impl.version>2.23.1</log4j-slf4j-impl.version>
        <disruptor.version>3.4.4</disruptor.version>

        <tongweb.version>8.0.E.3</tongweb.version>

        <context-propagation.version>1.1.3</context-propagation.version>

        <!--  Database  -->
        <oceanbase-client.version>2.4.14</oceanbase-client.version>
        <hikaricp.version>6.2.1</hikaricp.version>
        <micrometer.version>1.14.2</micrometer.version>
        <mysql-connector.version>8.4.0</mysql-connector.version>
        <mybatis.version>3.5.17</mybatis.version>
        <mybatis-spring-boot-starter.version>3.0.4</mybatis-spring-boot-starter.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>

        <!-- Swagger  -->
        <spring-restdocs-mockmvc.version>3.0.3</spring-restdocs-mockmvc.version>
        <!--  https://springdoc.org/#what-is-the-compatibility-matrix-of-springdoc-openapi-with-spring-boot -->
        <springdoc-openapi-starter-webmvc-ui.version>2.6.0</springdoc-openapi-starter-webmvc-ui.version>

        <!-- apollo client -->
        <apollo-client.version>2.3.0</apollo-client.version>

        <fastjson2.version>2.0.53</fastjson2.version>
        <commons-io.version>2.18.0</commons-io.version>
        <esb-auth.version>2.0.1</esb-auth.version>

        <!-- Kafka client -->
        <kafka-clients.version>3.7.2</kafka-clients.version>

        <!--  Testing -->
        <jacoco.version>0.8.12</jacoco.version>
        <junit-jupiter.version>5.10.2</junit-jupiter.version>
        <assertj-core.version>3.26.3</assertj-core.version>
        <caffeine.version>3.1.8</caffeine.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-bom</artifactId>
                <version>${slf4j-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-dependencies</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Boot  -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--  Testing -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--  Apollo -->
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo-client.version}</version>
            </dependency>

            <!-- Kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>

            <!-- webflux context -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>context-propagation</artifactId>
                <version>${context-propagation.version}</version>
            </dependency>

            <!-- Cache caffeine -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!--  Oceanbase  -->
            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase-client.version}</version>
            </dependency>

            <!-- MySql -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <!-- MyBatisPlus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <distributionManagement>
                <repository>
                    <id>local.repo</id>
                    <name>Local Repository</name>
                    <url>file:${project.build.directory}/local-repo</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>release</id>
            <name>maven-release</name>
            <url>https://bkrepo.chinastock.com.cn/maven/n614c6/maven-release/</url>
        </repository>
    </distributionManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <configuration>
                        <delimiters>
                            <delimiter>${*}</delimiter>
                        </delimiters>
                        <useDefaultDelimiters>false</useDefaultDelimiters>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
