package cn.com.chinastock.cnf.mdatasource.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ExecutorType;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;

import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 配置完整性测试
 * 验证MyBatis和MyBatis Plus的配置能够正确应用
 * 
 * <AUTHOR>
 */
public class ConfigurationTest {

    private final ResourceLoader resourceLoader = new DefaultResourceLoader();

    @Test
    public void testMybatisConfigurationApplier() {
        // 创建MyBatis配置
        org.mybatis.spring.boot.autoconfigure.MybatisProperties properties = new org.mybatis.spring.boot.autoconfigure.MybatisProperties();
        
        // 设置基本配置
        properties.setConfigLocation("classpath:mybatis-config.xml");
        properties.setMapperLocations(new String[]{"classpath*:mapper/**/*.xml"});
        properties.setTypeAliasesPackage("com.example.entity");
        properties.setTypeHandlersPackage("com.example.typehandler");
        properties.setExecutorType(ExecutorType.REUSE);
        
        // 设置配置属性
        Properties configProps = new Properties();
        configProps.setProperty("mapUnderscoreToCamelCase", "true");
        configProps.setProperty("cacheEnabled", "false");
        configProps.setProperty("lazyLoadingEnabled", "true");
        configProps.setProperty("defaultStatementTimeout", "30");
        configProps.setProperty("defaultFetchSize", "100");
        properties.setConfigurationProperties(configProps);
        
        // 创建SqlSessionFactoryBean
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        
        // 应用配置
        MybatisConfigurationApplier.applyConfiguration(factoryBean, properties, resourceLoader);
        
        // 验证配置是否正确应用
        assertNotNull(factoryBean.getConfigLocation());
        assertNotNull(factoryBean.getMapperLocations());
        assertEquals("com.example.entity", factoryBean.getTypeAliasesPackage());
        assertEquals("com.example.typehandler", factoryBean.getTypeHandlersPackage());
        assertEquals(ExecutorType.REUSE, factoryBean.getExecutorType());
        assertNotNull(factoryBean.getConfigurationProperties());
    }

    @Test
    public void testMybatisConfigurationCreation() {
        // 创建MyBatis配置属性
        org.mybatis.spring.boot.autoconfigure.MybatisProperties properties = new org.mybatis.spring.boot.autoconfigure.MybatisProperties();
        
        Properties configProps = new Properties();
        configProps.setProperty("mapUnderscoreToCamelCase", "true");
        configProps.setProperty("cacheEnabled", "false");
        configProps.setProperty("lazyLoadingEnabled", "true");
        configProps.setProperty("aggressiveLazyLoading", "false");
        configProps.setProperty("multipleResultSetsEnabled", "true");
        configProps.setProperty("useColumnLabel", "true");
        configProps.setProperty("useGeneratedKeys", "false");
        configProps.setProperty("defaultExecutorType", "SIMPLE");
        configProps.setProperty("defaultStatementTimeout", "25");
        configProps.setProperty("defaultFetchSize", "100");
        configProps.setProperty("safeRowBoundsEnabled", "false");
        configProps.setProperty("safeResultHandlerEnabled", "true");
        configProps.setProperty("callSettersOnNulls", "false");
        configProps.setProperty("useActualParamName", "true");
        configProps.setProperty("returnInstanceForEmptyRow", "false");
        configProps.setProperty("logPrefix", "mybatis.");
        
        properties.setConfigurationProperties(configProps);
        
        // 创建Configuration
        Configuration configuration = MybatisConfigurationApplier.createConfiguration(properties);
        
        // 验证配置
        assertTrue(configuration.isMapUnderscoreToCamelCase());
        assertFalse(configuration.isCacheEnabled());
        assertTrue(configuration.isLazyLoadingEnabled());
        assertFalse(configuration.isAggressiveLazyLoading());
        assertTrue(configuration.isMultipleResultSetsEnabled());
        assertTrue(configuration.isUseColumnLabel());
        assertFalse(configuration.isUseGeneratedKeys());
        assertEquals(ExecutorType.SIMPLE, configuration.getDefaultExecutorType());
        assertEquals(25, configuration.getDefaultStatementTimeout().intValue());
        assertEquals(100, configuration.getDefaultFetchSize().intValue());
        assertFalse(configuration.isSafeRowBoundsEnabled());
        assertTrue(configuration.isSafeResultHandlerEnabled());
        assertFalse(configuration.isCallSettersOnNulls());
        assertTrue(configuration.isUseActualParamName());
        assertFalse(configuration.isReturnInstanceForEmptyRow());
        assertEquals("mybatis.", configuration.getLogPrefix());
    }

    @Test
    public void testMybatisPlusConfigurationApplier() {
        // 创建MyBatis Plus配置
        MybatisPlusProperties properties = new MybatisPlusProperties();
        
        // 设置基本配置
        properties.setConfigLocation("classpath:mybatis-config.xml");
        properties.setMapperLocations(new String[]{"classpath*:mapper/**/*.xml"});
        properties.setTypeAliasesPackage("com.example.entity");
        properties.setTypeHandlersPackage("com.example.typehandler");
        properties.setTypeEnumsPackage("com.example.enums");
        properties.setExecutorType(ExecutorType.BATCH);
        
        // 设置配置属性
        Properties configProps = new Properties();
        configProps.setProperty("mapUnderscoreToCamelCase", "true");
        configProps.setProperty("cacheEnabled", "true");
        configProps.setProperty("lazyLoadingEnabled", "false");
        properties.setConfigurationProperties(configProps);
        
        // 设置全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setTablePrefix("t_");
        dbConfig.setLogicDeleteField("deleted");
        dbConfig.setLogicDeleteValue("1");
        dbConfig.setLogicNotDeleteValue("0");
        globalConfig.setDbConfig(dbConfig);
        properties.setGlobalConfig(globalConfig);
        
        // 创建MybatisSqlSessionFactoryBean
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        
        // 应用配置
        MybatisPlusConfigurationApplier.applyConfiguration(factoryBean, properties, resourceLoader);
        
        // 验证配置是否正确应用
        assertNotNull(factoryBean.getConfigLocation());
        assertNotNull(factoryBean.getMapperLocations());
        assertEquals("com.example.entity", factoryBean.getTypeAliasesPackage());
        assertEquals("com.example.typehandler", factoryBean.getTypeHandlersPackage());
        assertEquals("com.example.enums", factoryBean.getTypeEnumsPackage());
        assertEquals(ExecutorType.BATCH, factoryBean.getExecutorType());
        assertNotNull(factoryBean.getConfigurationProperties());
        assertNotNull(factoryBean.getGlobalConfig());
    }

    @Test
    public void testMybatisPlusConfigurationCreation() {
        // 创建MyBatis Plus配置属性
        MybatisPlusProperties properties = new MybatisPlusProperties();
        
        Properties configProps = new Properties();
        configProps.setProperty("mapUnderscoreToCamelCase", "true");
        configProps.setProperty("cacheEnabled", "true");
        configProps.setProperty("lazyLoadingEnabled", "false");
        configProps.setProperty("defaultStatementTimeout", "30");
        configProps.setProperty("defaultFetchSize", "50");
        
        properties.setConfigurationProperties(configProps);
        
        // 创建MybatisConfiguration
        MybatisConfiguration configuration = MybatisPlusConfigurationApplier.createMybatisConfiguration(properties);
        
        // 验证配置
        assertTrue(configuration.isMapUnderscoreToCamelCase());
        assertTrue(configuration.isCacheEnabled());
        assertFalse(configuration.isLazyLoadingEnabled());
        assertEquals(30, configuration.getDefaultStatementTimeout().intValue());
        assertEquals(50, configuration.getDefaultFetchSize().intValue());
    }

    @Test
    public void testMybatisPlusGlobalConfigCreation() {
        // 创建MyBatis Plus配置属性
        MybatisPlusProperties properties = new MybatisPlusProperties();
        
        // 设置全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setTablePrefix("sys_");
        dbConfig.setLogicDeleteField("is_deleted");
        dbConfig.setLogicDeleteValue("1");
        dbConfig.setLogicNotDeleteValue("0");
        globalConfig.setDbConfig(dbConfig);
        properties.setGlobalConfig(globalConfig);
        
        // 创建GlobalConfig
        GlobalConfig createdGlobalConfig = MybatisPlusConfigurationApplier.createGlobalConfig(properties);
        
        // 验证配置
        assertNotNull(createdGlobalConfig);
        assertNotNull(createdGlobalConfig.getDbConfig());
        assertEquals("sys_", createdGlobalConfig.getDbConfig().getTablePrefix());
        assertEquals("is_deleted", createdGlobalConfig.getDbConfig().getLogicDeleteField());
        assertEquals("1", createdGlobalConfig.getDbConfig().getLogicDeleteValue());
        assertEquals("0", createdGlobalConfig.getDbConfig().getLogicNotDeleteValue());
    }

    @Test
    public void testUnknownConfigurationProperty() {
        // 测试未知配置项的处理
        org.mybatis.spring.boot.autoconfigure.MybatisProperties properties = new org.mybatis.spring.boot.autoconfigure.MybatisProperties();
        
        Properties configProps = new Properties();
        configProps.setProperty("unknownProperty", "someValue");
        configProps.setProperty("mapUnderscoreToCamelCase", "true");
        
        properties.setConfigurationProperties(configProps);
        
        // 创建Configuration（应该不会抛出异常）
        Configuration configuration = MybatisConfigurationApplier.createConfiguration(properties);
        
        // 验证已知配置项仍然正确应用
        assertTrue(configuration.isMapUnderscoreToCamelCase());
    }
}
