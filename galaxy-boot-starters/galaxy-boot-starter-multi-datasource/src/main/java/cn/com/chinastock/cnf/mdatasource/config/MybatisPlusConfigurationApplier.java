package cn.com.chinastock.cnf.mdatasource.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.ExecutorType;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * MyBatis Plus配置应用工具类
 * 负责将MybatisPlusProperties中的配置应用到MybatisSqlSessionFactoryBean
 * 
 * <AUTHOR>
 */
public class MybatisPlusConfigurationApplier {

    /**
     * 应用MyBatis Plus配置到MybatisSqlSessionFactoryBean
     * 
     * @param factoryBean MybatisSqlSessionFactoryBean实例
     * @param properties MyBatis Plus配置属性
     * @param resourceLoader 资源加载器
     */
    public static void applyConfiguration(MybatisSqlSessionFactoryBean factoryBean, 
                                        MybatisPlusProperties properties, 
                                        ResourceLoader resourceLoader) {
        
        // 设置配置文件位置
        if (StringUtils.hasText(properties.getConfigLocation())) {
            Resource configResource = resourceLoader.getResource(properties.getConfigLocation());
            factoryBean.setConfigLocation(configResource);
        }
        
        // 设置Mapper XML文件位置
        if (!ObjectUtils.isEmpty(properties.getMapperLocations())) {
            List<Resource> mapperResources = new ArrayList<>();
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(resourceLoader);
            
            for (String location : properties.getMapperLocations()) {
                try {
                    Resource[] resources = resolver.getResources(location);
                    for (Resource resource : resources) {
                        if (resource.exists()) {
                            mapperResources.add(resource);
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to load mapper resources from: " + location, e);
                }
            }
            
            if (!mapperResources.isEmpty()) {
                factoryBean.setMapperLocations(mapperResources.toArray(new Resource[0]));
            }
        }
        
        // 设置类型别名包路径
        if (StringUtils.hasText(properties.getTypeAliasesPackage())) {
            factoryBean.setTypeAliasesPackage(properties.getTypeAliasesPackage());
        }
        
        // 设置类型别名父类型
        if (properties.getTypeAliasesSuperType() != null) {
            factoryBean.setTypeAliasesSuperType(properties.getTypeAliasesSuperType());
        }
        
        // 设置类型处理器包路径
        if (StringUtils.hasText(properties.getTypeHandlersPackage())) {
            factoryBean.setTypeHandlersPackage(properties.getTypeHandlersPackage());
        }
        
        // 设置类型枚举包路径
        if (StringUtils.hasText(properties.getTypeEnumsPackage())) {
            factoryBean.setTypeEnumsPackage(properties.getTypeEnumsPackage());
        }
        
        // 设置执行器类型
        if (properties.getExecutorType() != null) {
            factoryBean.setExecutorType(properties.getExecutorType());
        }
        
        // 设置配置属性
        if (!ObjectUtils.isEmpty(properties.getConfigurationProperties())) {
            factoryBean.setConfigurationProperties(properties.getConfigurationProperties());
        }
        
        // 设置MyBatis Configuration
        MybatisConfiguration configuration = createMybatisConfiguration(properties);
        if (configuration != null) {
            factoryBean.setConfiguration(configuration);
        }
        
        // 设置全局配置
        GlobalConfig globalConfig = createGlobalConfig(properties);
        if (globalConfig != null) {
            factoryBean.setGlobalConfig(globalConfig);
        }
    }
    
    /**
     * 创建并配置MyBatis Plus Configuration对象
     * 
     * @param properties MyBatis Plus配置属性
     * @return 配置好的MybatisConfiguration对象
     */
    public static MybatisConfiguration createMybatisConfiguration(MybatisPlusProperties properties) {
        MybatisConfiguration configuration = new MybatisConfiguration();
        
        // 如果已经有Configuration对象，使用现有的
        if (properties.getConfiguration() != null) {
            return properties.getConfiguration();
        }
        
        // 从配置属性中创建Configuration
        Properties configProps = properties.getConfigurationProperties();
        if (configProps != null) {
            configProps.forEach((key, value) -> {
                String keyStr = key.toString();
                String valueStr = value.toString();
                
                // 应用常用配置项
                switch (keyStr) {
                    case "mapUnderscoreToCamelCase":
                        configuration.setMapUnderscoreToCamelCase(Boolean.parseBoolean(valueStr));
                        break;
                    case "cacheEnabled":
                        configuration.setCacheEnabled(Boolean.parseBoolean(valueStr));
                        break;
                    case "lazyLoadingEnabled":
                        configuration.setLazyLoadingEnabled(Boolean.parseBoolean(valueStr));
                        break;
                    case "aggressiveLazyLoading":
                        configuration.setAggressiveLazyLoading(Boolean.parseBoolean(valueStr));
                        break;
                    case "multipleResultSetsEnabled":
                        configuration.setMultipleResultSetsEnabled(Boolean.parseBoolean(valueStr));
                        break;
                    case "useColumnLabel":
                        configuration.setUseColumnLabel(Boolean.parseBoolean(valueStr));
                        break;
                    case "useGeneratedKeys":
                        configuration.setUseGeneratedKeys(Boolean.parseBoolean(valueStr));
                        break;
                    case "defaultExecutorType":
                        configuration.setDefaultExecutorType(ExecutorType.valueOf(valueStr));
                        break;
                    case "defaultStatementTimeout":
                        configuration.setDefaultStatementTimeout(Integer.parseInt(valueStr));
                        break;
                    case "defaultFetchSize":
                        configuration.setDefaultFetchSize(Integer.parseInt(valueStr));
                        break;
                    case "safeRowBoundsEnabled":
                        configuration.setSafeRowBoundsEnabled(Boolean.parseBoolean(valueStr));
                        break;
                    case "safeResultHandlerEnabled":
                        configuration.setSafeResultHandlerEnabled(Boolean.parseBoolean(valueStr));
                        break;
                    case "callSettersOnNulls":
                        configuration.setCallSettersOnNulls(Boolean.parseBoolean(valueStr));
                        break;
                    case "useActualParamName":
                        configuration.setUseActualParamName(Boolean.parseBoolean(valueStr));
                        break;
                    case "returnInstanceForEmptyRow":
                        configuration.setReturnInstanceForEmptyRow(Boolean.parseBoolean(valueStr));
                        break;
                    case "logPrefix":
                        configuration.setLogPrefix(valueStr);
                        break;
                    case "configurationFactory":
                        try {
                            Class<?> factoryClass = Class.forName(valueStr);
                            configuration.setConfigurationFactory(factoryClass);
                        } catch (ClassNotFoundException e) {
                            throw new RuntimeException("Configuration factory class not found: " + valueStr, e);
                        }
                        break;
                    default:
                        // 对于不识别的配置项，记录警告日志
                        System.out.println("Unknown MyBatis Plus configuration property: " + keyStr);
                        break;
                }
            });
        }
        
        return configuration;
    }
    
    /**
     * 创建并配置GlobalConfig对象
     * 
     * @param properties MyBatis Plus配置属性
     * @return 配置好的GlobalConfig对象
     */
    public static GlobalConfig createGlobalConfig(MybatisPlusProperties properties) {
        GlobalConfig globalConfig = properties.getGlobalConfig();
        
        if (globalConfig == null) {
            globalConfig = new GlobalConfig();
        }
        
        return globalConfig;
    }
}
