package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.config.MybatisPlusConfigurationApplier;
import cn.com.chinastock.cnf.mdatasource.factory.DataSourceFactory;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;

/**
 * MyBatisPlus数据源注册器
 * 负责注册MyBatisPlus相关的Bean定义
 *
 * <AUTHOR>
 */
public class MybatisPlusDataSourceRegistrar implements DataSourceRegistrar {
    
    @Override
    public boolean supports(DataSourceDefinition properties) {
        return properties.getType() == DataSourceType.MYBATIS_PLUS;
    }
    
    @Override
    public void registerDataSource(BeanDefinitionRegistry registry, 
                                 String datasourceName, 
                                 DataSourceDefinition properties, 
                                 ResourceLoader resourceLoader) {
        
        String dataSourceBeanName = datasourceName + "DataSource";
        String sqlSessionFactoryBeanName = datasourceName + "SqlSessionFactory";
        String sqlSessionTemplateBeanName = datasourceName + "SqlSessionTemplate";
        String transactionManagerBeanName = datasourceName + "TransactionManager";

        // 注册DataSource
        registerDataSourceBean(registry, dataSourceBeanName, properties);
        
        // 注册SqlSessionFactory
        registerSqlSessionFactoryBean(registry, sqlSessionFactoryBeanName, 
                                    dataSourceBeanName, properties, resourceLoader);
        
        // 注册SqlSessionTemplate
        registerSqlSessionTemplateBean(registry, sqlSessionTemplateBeanName, 
                                     sqlSessionFactoryBeanName, properties);
        
        // 注册TransactionManager
        registerTransactionManagerBean(registry, transactionManagerBeanName, 
                                     dataSourceBeanName, properties);
        
        // 注册MyBatisPlus Mappers
        registerMybatisPlusMappers(registry, properties, sqlSessionFactoryBeanName, resourceLoader);
    }
    
    private void registerDataSourceBean(BeanDefinitionRegistry registry,
                                      String beanName,
                                      DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class,
                () -> DataSourceFactory.createDataSource(properties, beanName));
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionFactoryBean(BeanDefinitionRegistry registry,
                                             String beanName,
                                             String dataSourceBeanName,
                                             DataSourceDefinition properties,
                                             ResourceLoader resourceLoader) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MybatisSqlSessionFactoryBean.class,
                () -> {
                    MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();

                    // 应用MyBatis Plus配置
                    com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties mybatisPlusProps = properties.getMybatisPlus();
                    MybatisPlusConfigurationApplier.applyConfiguration(factoryBean, mybatisPlusProps, resourceLoader);

                    return factoryBean;
                });

        builder.addPropertyReference("dataSource", dataSourceBeanName);

        
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionTemplateBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String sqlSessionFactoryBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(SqlSessionTemplate.class);
        builder.addConstructorArgReference(sqlSessionFactoryBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerTransactionManagerBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String dataSourceBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSourceTransactionManager.class);
        builder.addConstructorArgReference(dataSourceBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerMybatisPlusMappers(BeanDefinitionRegistry registry, 
                                          DataSourceDefinition properties, 
                                          String sqlSessionFactoryBeanName,
                                          ResourceLoader resourceLoader) {
        ClassPathScanningCandidateComponentProvider scanner = new MapperScanner(resourceLoader);

        properties.getPackages().getMapper().forEach(basePackage -> {
            scanner.findCandidateComponents(basePackage).forEach(beanDefinition -> {
                Class<?> mapperClass = classForName(beanDefinition.getBeanClassName());
                String beanName = StringUtils.uncapitalize(mapperClass.getSimpleName());

                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MapperFactoryBean.class);
                builder.addConstructorArgValue(mapperClass);
                builder.addPropertyReference("sqlSessionFactory", sqlSessionFactoryBeanName);

                registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
            });
        });
    }
    
    private Class<?> classForName(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Failed to load class: " + className, e);
        }
    }
    
    private void registerBean(String beanName, BeanDefinitionRegistry registry, 
                            DataSourceDefinition properties, BeanDefinitionBuilder builder) {
        if (properties.isPrimary()) {
            builder.setPrimary(true);
        }
        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }
    
    /**
     * Mapper扫描器，用于扫描Mapper接口
     */
    private static class MapperScanner extends ClassPathScanningCandidateComponentProvider {
        
        public MapperScanner(ResourceLoader resourceLoader) {
            super(false);
            setResourceLoader(resourceLoader);
            addIncludeFilter(new AnnotationTypeFilter(Mapper.class));
            addIncludeFilter(new AssignableTypeFilter(BaseMapper.class));
        }
        
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface() && beanDefinition.getMetadata().isIndependent();
        }
    }
}
