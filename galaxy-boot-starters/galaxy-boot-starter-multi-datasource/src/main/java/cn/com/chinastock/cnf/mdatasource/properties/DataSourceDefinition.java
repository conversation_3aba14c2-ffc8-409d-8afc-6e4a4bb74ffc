package cn.com.chinastock.cnf.mdatasource.properties;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;

/**
 * DataSourceDefinition 类定义单个数据源的所有配置信息。
 * 该类包含了标准的Spring Boot数据源配置、JPA配置、MyBatis配置以及包路径配置，
 * 用于在多数据源环境中配置每个独立的数据源。
 *
 * <AUTHOR>
 */
public class DataSourceDefinition {

    private boolean primary = false;

    /**
     * 数据源类型，支持 jpa 和 mybatis
     */
    private DataSourceType type = DataSourceType.JPA;
    
    /**
     * 对应 `spring.datasource:` 标准的 Spring Boot 数据源属性
     */
    private DataSourceProperties datasource = new DataSourceProperties();

    /**
     * 对应 `spring.datasource.jpa:` 标准的 Spring Boot JPA 属性
     */
    private JpaProperties jpa = new JpaProperties();

    /**
     * MyBatis配置属性 - 使用官方配置类
     */
    private org.mybatis.spring.boot.autoconfigure.MybatisProperties mybatis = new org.mybatis.spring.boot.autoconfigure.MybatisProperties();

    /**
     * MyBatisPlus配置属性 - 使用官方配置类
     */
    private com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties mybatisPlus = new com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties();

    /**
     * Hikari连接池配置属性
     */
    private HikariProperties hikari = new HikariProperties();

    private ScopePackagesProperties packages = new ScopePackagesProperties();

    /**
     * 获取数据源配置属性
     * 
     * @return 数据源配置属性对象
     */
    public DataSourceProperties getDatasource() {
        return datasource;
    }

    /**
     * 设置数据源配置属性
     * 
     * @param datasource 数据源配置属性对象
     */
    public void setDatasource(DataSourceProperties datasource) {
        this.datasource = datasource;
    }

    /**
     * 获取JPA配置属性
     * 
     * @return JPA配置属性对象
     */
    public JpaProperties getJpa() {
        return jpa;
    }

    /**
     * 设置JPA配置属性
     * 
     * @param jpa JPA配置属性对象
     */
    public void setJpa(JpaProperties jpa) {
        this.jpa = jpa;
    }

    /**
     * 获取包路径配置
     * 
     * @return 包路径配置对象
     */
    public ScopePackagesProperties getPackages() {
        return packages;
    }

    /**
     * 设置包路径配置
     * 
     * @param packages 包路径配置对象
     */
    public void setPackages(ScopePackagesProperties packages) {
        this.packages = packages;
    }

    /**
     * 判断是否为主数据源
     * 
     * @return true表示是主数据源，false表示不是
     */
    public boolean isPrimary() {
        return primary;
    }

    /**
     * 设置是否为主数据源
     *
     * @param primary true表示设为主数据源，false表示不是主数据源
     */
    public void setPrimary(boolean primary) {
        this.primary = primary;
    }

    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    public DataSourceType getType() {
        return type;
    }

    /**
     * 设置数据源类型
     *
     * @param type 数据源类型
     */
    public void setType(DataSourceType type) {
        this.type = type;
    }

    /**
     * 获取MyBatis配置属性
     *
     * @return MyBatis配置属性对象
     */
    public org.mybatis.spring.boot.autoconfigure.MybatisProperties getMybatis() {
        return mybatis;
    }

    /**
     * 设置MyBatis配置属性
     *
     * @param mybatis MyBatis配置属性对象
     */
    public void setMybatis(org.mybatis.spring.boot.autoconfigure.MybatisProperties mybatis) {
        this.mybatis = mybatis;
    }

    /**
     * 获取MyBatisPlus配置属性
     *
     * @return MyBatisPlus配置属性对象
     */
    public com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties getMybatisPlus() {
        return mybatisPlus;
    }

    /**
     * 设置MyBatisPlus配置属性
     *
     * @param mybatisPlus MyBatisPlus配置属性对象
     */
    public void setMybatisPlus(com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties mybatisPlus) {
        this.mybatisPlus = mybatisPlus;
    }

    /**
     * 获取Hikari连接池配置属性
     *
     * @return Hikari连接池配置属性对象
     */
    public HikariProperties getHikari() {
        return hikari;
    }

    /**
     * 设置Hikari连接池配置属性
     *
     * @param hikari Hikari连接池配置属性对象
     */
    public void setHikari(HikariProperties hikari) {
        this.hikari = hikari;
    }
}