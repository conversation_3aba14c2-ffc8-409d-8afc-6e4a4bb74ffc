# 多数据源配置改进说明

## 概述

本次改进解决了原有MyBatis和MyBatis Plus配置处理不完整的问题，现在支持官方starter的所有配置项。

## 主要改进

### 1. 使用官方配置类

**改进前：**
- 使用自定义的 `MybatisProperties` 和 `MybatisPlusProperties`
- 只支持少量配置项
- 与官方文档不一致

**改进后：**
- 直接使用官方配置类：
  - `org.mybatis.spring.boot.autoconfigure.MybatisProperties`
  - `com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties`
- 支持所有官方配置项
- 与官方文档完全一致

### 2. 完整的配置支持

#### MyBatis 配置支持

现在支持所有 `mybatis-spring-boot-starter` 的配置项：

```yaml
mybatis:
  # 基本配置
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.example.entity
  type-aliases-super-type: com.example.BaseEntity
  type-handlers-package: com.example.typehandler
  executor-type: REUSE
  check-config-location: true
  default-scripting-language-driver: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
  lazy-initialization: false
  mapper-default-scope: singleton
  inject-sql-session-on-mapper-scan: true
  
  # 详细配置属性
  configuration-properties:
    mapUnderscoreToCamelCase: true
    cacheEnabled: true
    lazyLoadingEnabled: true
    aggressiveLazyLoading: false
    multipleResultSetsEnabled: true
    useColumnLabel: true
    useGeneratedKeys: true
    defaultExecutorType: SIMPLE
    defaultStatementTimeout: 30
    defaultFetchSize: 100
    safeRowBoundsEnabled: false
    safeResultHandlerEnabled: true
    callSettersOnNulls: false
    useActualParamName: true
    returnInstanceForEmptyRow: false
    logPrefix: "mybatis."
    configurationFactory: com.example.MyConfigurationFactory
```

#### MyBatis Plus 配置支持

现在支持所有 `mybatis-plus-spring-boot3-starter` 的配置项：

```yaml
mybatis-plus:
  # 基本配置
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.example.entity
  type-aliases-super-type: com.example.BaseEntity
  type-handlers-package: com.example.typehandler
  type-enums-package: com.example.enums
  executor-type: BATCH
  check-config-location: true
  lazy-initialization: false
  mapper-default-scope: singleton
  inject-sql-session-on-mapper-scan: true
  
  # 配置属性
  configuration-properties:
    mapUnderscoreToCamelCase: true
    cacheEnabled: false
    lazyLoadingEnabled: false
    defaultStatementTimeout: 25
    defaultFetchSize: 50
    # ... 所有MyBatis配置项
    
  # 全局配置
  global-config:
    banner: true
    enable-sql-runner: true
    
    db-config:
      # 主键配置
      id-type: ASSIGN_ID
      
      # 表名配置
      table-prefix: t_
      schema: my_schema
      table-underline: true
      capital-mode: false
      
      # 字段配置
      column-format: "%s"
      property-format: "%s"
      table-format: "%s"
      
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      
      # 字段策略配置
      insert-strategy: NOT_NULL
      update-strategy: NOT_NULL
      where-strategy: NOT_NULL
```

### 3. 配置应用工具类

创建了专门的配置应用工具类来处理复杂的配置逻辑：

- `MybatisConfigurationApplier`: 处理MyBatis配置
- `MybatisPlusConfigurationApplier`: 处理MyBatis Plus配置

这些工具类：
- 简化了registrar中的配置逻辑
- 提供了完整的配置项支持
- 包含配置验证和错误处理
- 支持未知配置项的优雅处理

### 4. 参考JPA的实现方式

改进参考了JPA的实现方式：
- 直接使用官方配置类，如 `JpaProperties`
- 通过工厂类处理复杂的配置逻辑，如 `EntityManagerFactoryBuilderFactory`
- 保持与Spring Boot官方starter的一致性

## 使用示例

### 完整配置示例

参见 `src/test/resources/application-test.yml` 文件，其中包含了：
- MyBatis数据源的完整配置示例
- MyBatis Plus数据源的完整配置示例
- 各种高级配置选项的使用方法

### 测试用例

参见 `src/test/java/cn/com/chinastock/cnf/mdatasource/config/ConfigurationTest.java`，包含：
- 配置应用的完整性测试
- 各种配置项的验证测试
- 错误处理的测试

## 兼容性

### 向后兼容

- 现有的配置仍然有效
- 不会破坏现有的应用程序

### 向前兼容

- 支持官方starter的所有新特性
- 自动跟随官方配置类的更新

## 优势

1. **完整性**: 支持所有官方配置项
2. **一致性**: 与官方文档和starter保持一致
3. **可维护性**: 使用官方配置类，减少维护成本
4. **可扩展性**: 自动支持官方新增的配置项
5. **可靠性**: 包含完整的测试用例和错误处理

## 迁移指南

如果您之前使用了自定义的配置项，请参考官方文档进行调整：

- MyBatis: https://mybatis.org/spring-boot-starter/mybatis-spring-boot-autoconfigure/
- MyBatis Plus: https://baomidou.com/en/reference/

大部分配置项名称保持不变，只是现在支持更多的配置选项。
